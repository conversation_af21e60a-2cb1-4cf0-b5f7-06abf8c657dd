<template>
    <view class="user-profile">
        <!-- 用户信息区域 -->
        <view class="content">
            <!-- 个人信息区域 -->
            <view class="profile-info">
                <!-- 用户基本信息区域 -->
                <view class="user-info-section">
                    <!-- 头像和基本信息 -->
                    <view class="user-main-info">
                        <view class="avatar-container" @click="previewImage">
                            <u-avatar :src="info.headimgurl" size="150" mode="aspectFill" class="custom-avatar"></u-avatar>
                        </view>
                        <view class="info-container">
                            <!-- 第一行：昵称和在线时间 -->
                            <view class="user-nickname-row">
                                <text class="user-nickname">{{ info.nickname }}</text>
                                <text class="online-time" v-if="info.addtime">{{ $util.formatTimeString(info.addtime) }}</text>
                            </view>

                            <!-- 第二行：性别、年龄、地区 -->
                            <view class="user-detail-row">
                                <view class="gender-tag" :class="{ male: isMale, female: !isMale }">
                                    <u-icon :name="isMale ? 'man' : 'woman'" size="24" color="#fff"></u-icon>
                                </view>
                                <view class="info-tag" v-if="generationLabel">
                                    <u-icon name="calendar" color="#8966ef" size="26"></u-icon>
                                    <text>{{ generationLabel }}</text>
                                </view>
                                <view class="info-tag location-tag" v-if="fullLocation" :title="fullLocation">
                                    <u-icon name="map-fill" color="#8966ef" size="26"></u-icon>
                                    <text class="location-text">{{ fullLocation }}</text>
                                </view>
                            </view>
                        </view>
                    </view>

                    <!-- 右上角点赞按钮 -->
                </view>

                <!-- 个人简介 -->
                <view class="user-intro" v-if="info.note">
                    <text>{{ info.note }}</text>
                </view>

                <!-- 标签列表 -->
                <view class="user-tags" v-if="hasTags">
                    <view>
                        <view class="tag-item" v-for="(tag, index) in info.tag_data_arr" :key="tag.id">
                            <text>{{ tag.name }}</text>
                        </view>
                    </view>
                </view>
                <!-- 分隔线 -->
                <view class="divider" v-if="!isOwnProfile"></view>
                <!-- 操作按钮区域 -->
                <view class="action-buttons-section" v-if="!isOwnProfile">
                    <view class="action-buttons">
                        <button class="gradient-btn private-chat-btn" @click="handlePrivateChat" hover-class="btn-hover">
                            <text>私聊</text>
                        </button>
                        <button class="gradient-btn wechat-btn" @click="showVipPopup" hover-class="btn-hover">
                            <text>{{ isValidMember ? '查看微信' : '加微信' }}</text>
                        </button>
                        <button class="gradient-btn gift-btn" @click="showRewardPopup" hover-class="btn-hover">
                            <text>送礼物</text>
                        </button>
                          <button class="gradient-btn gift-btn" @click="handleFollow" v-if="workInfo.is_follow == 0" hover-class="btn-hover">
                            <text>关注</text>
                        </button>
                    </view>
                </view>
            </view>

            <!-- 作品列表 -->
            <view class="works-section">
                <view class="section-header">
                    <u-icon name="photo" size="20" color="#8966ef"></u-icon>
                    <text class="section-title">作品集</text>
                </view>
                <view class="user-grid">
                    <!-- 用户卡片网格，每行两个 -->
                    <view class="user-grid-item" v-for="(work, workIndex) in worksList" :key="workIndex">
                        <user-card :work-info="work" :hide-user-info="true" mode="profile"></user-card>
                    </view>
                </view>

                <!-- 加载更多 -->
                <view v-if="hasMoreWorks" class="load-more">
                    <view class="load-more-btn" @click="loadMoreWorks" :class="{ loading: isLoading }">
                        <u-loading v-if="isLoading" mode="circle" size="24" color="#8966ef"></u-loading>
                        <text>{{ isLoading ? '加载中...' : '查看更多' }}</text>
                    </view>
                </view>

                <!-- 无作品提示 -->
                <view v-if="!isLoading && worksList.length === 0" class="empty-works">
                    <u-empty mode="list" text="暂无作品"></u-empty>
                </view>
            </view>
        </view>
        <login-popup v-model="showLoginPopup" @close="handleLoginPopupClose" @login="handleLoginAction"></login-popup>

        <vip-popup v-model="showVip" @single-pay="handleSinglePay" @member-pay="handleMemberPay" @close="handleClose"></vip-popup>
        <reward-popup v-model="showReward" :work-id="id" :author-info="info" @reward-success="handleRewardSuccess" @close="handleClose"></reward-popup>
        <wechat-info-popup v-model="showWechatInfoPopup" :user-info="info" :need-decode-wechat="true" @close="handleWechatInfoClose"></wechat-info-popup>
    </view>
</template>

<script>
import vipPopup from '@/components/vip-popup.vue';
import rewardPopup from '@/components/reward-popup.vue';
import UserCard from '@/components/UserCard.vue';
import loginPopup from '@/components/login-popup.vue';
import wechatInfoPopup from '@/components/wechat-info-popup.vue';
import { mapState, mapActions } from 'vuex';

export default {
    components: {
        vipPopup,
        rewardPopup,
        UserCard,
        loginPopup,
        wechatInfoPopup,
    },
    data() {
        return {
            showVip: false,
            showReward: false,
            userId: '', // 用户ID
            id: '',
            info: {}, // 用户详细信息
            isFollowed: false, // 是否已关注
            showLoginPopup: false,
            showWechatInfoPopup: false,
            // 礼物商品ID配置
            giftGoodIds: {
                flower10: 1,
                flower20: 2,
                flower30: 3,
            },
            // 作品列表相关
            worksList: [],
            page: 1,
            pagesize: 10,
            hasMoreWorks: true,
            isLoading: false,
            address:'',
        };
    },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),

        // 微信号中间用***遮盖
        maskedWechat() {
            const wechat = this.$util.decodeBase64(this.info?.weixin) || '';
            if (wechat.length <= 4) return wechat;
            const start = wechat.substring(0, 2);
            const end = wechat.substring(wechat.length - 2);
            return `${start}******${end}`;
        },
        // 判断性别
        isMale() {
            return this.info?.sex === '1';
        },
        // 计算年龄
        formatAge() {
            if (!this.info?.birthday) return '';
            const birthDate = new Date(this.info.birthday.replace(/-/g, '/'));
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
            }
            return age;
        },
        // 只显示城市名称
        cityOnly() {
            if (!this.info?.city_code_txt?.name) {
                return this.info?.province_code_txt?.name || '未知地区';
            }

            const city = this.info.city_code_txt.name;
            // 如果城市名称包含"自治区"等字样，使用区域名称
            if (city.includes('自治区') || city.includes('行政区划')) {
                return this.info.area_code_txt?.name || city;
            }
            return city;
        },
        // 判断是否有标签
        hasTags() {
            return this.info?.tag_data_arr && this.info.tag_data_arr.length > 0;
        },
        // 计算年代标识（80后、90后等）
        generationLabel() {
            if (!this.info?.birthday) return '';
            const birthDate = new Date(this.info.birthday.replace(/-/g, '/'));
            const birthYear = birthDate.getFullYear();

            if (birthYear >= 1980 && birthYear < 1985) {
                return '80+';
            } else if (birthYear >= 1985 && birthYear < 1990) {
                return '85+';
            } else if (birthYear >= 1990 && birthYear < 1995) {
                return '90+';
            } else if (birthYear >= 1995 && birthYear < 2000) {
                return '95+';
            } else if (birthYear >= 2000) {
                return '00+';
            } else if (birthYear >= 1975) {
                return '75+';
            } else if (birthYear >= 1970) {
                return '70+';
            }
            return '';
        },
        // 完整地理位置：优先使用address字段，不存在则使用用户信息的地址
        // 统一格式：市-区，市取前四位不含"市"字，区取2位
        // 特殊处理：新疆、内蒙古等自治区显示为"自治区-城市"
        fullLocation() {
            // 优先使用address字段，但也要格式化
            if (this.address && this.address.trim()) {
                return this.formatAddressString(this.address);
            }
            // address不存在，使用用户信息的地址
            return this.formatUserLocation();
        },
        // 判断是否为有效会员
        isValidMember() {
            if (!this.userInfo || !this.userInfo.member_time) {
                return false;
            }

            // 如果member_time为0或空字符串，表示非会员
            if (this.userInfo.member_time === '0' || this.userInfo.member_time === 0 || this.userInfo.member_time === '') {
                return false;
            }

            // 将member_time转换为时间戳进行比较
            let memberExpireTime;

            // 如果member_time是时间戳格式（数字）
            if (typeof this.userInfo.member_time === 'number' || /^\d+$/.test(this.userInfo.member_time)) {
                memberExpireTime = parseInt(this.userInfo.member_time) * 1000; // 转换为毫秒
            }
            // 如果member_time是日期字符串格式
            else if (typeof this.userInfo.member_time === 'string') {
                memberExpireTime = new Date(this.userInfo.member_time.replace(/-/g, '/')).getTime();
            } else {
                return false;
            }

            // 比较当前时间和会员到期时间
            const currentTime = new Date().getTime();
            return memberExpireTime > currentTime;
        },
        // 判断是否为自己的个人资料
        isOwnProfile() {
            return this.info && this.userInfo && this.info.uid === this.userInfo.uid;
        },
    },
    onLoad(e) {
        this.userId = e.uid;
        this.id = e.id;
        if(e.address){
            this.address = e.address;
        }
        this.getUserInfo();
        this.getUserWorksList();
    },
    methods: {
        // 获取用户信息
        async getUserInfo() {
            try {
                const res = await this.$api.getUserInfo({
                    to_uid: this.userId,
                    access_token: uni.getStorageSync('token'),
                });
                this.info = res.data;
                if(!this.address){
                    this.formatUserLocation()
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'none',
                });
            }
        },

        // 获取用户作品列表
        async getUserWorksList(loadMore = false) {
            try {
                if (this.isLoading) return;

                this.isLoading = true;

                if (!loadMore) {
                    this.page = 1;
                    this.worksList = [];
                    this.hasMoreWorks = true;
                }

                const res = await this.$api.getUserWorkList({
                    to_uid: this.userId,
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                const newWorks = res.data.list || [];

                // 判断是否还有更多数据
                this.hasMoreWorks = newWorks.length >= this.pagesize;

                // 更新作品列表
                if (loadMore) {
                    this.worksList = [...this.worksList, ...newWorks];
                } else {
                    this.worksList = newWorks;
                }
            } catch (error) {
                console.error('获取作品列表失败:', error);
                uni.showToast({
                    title: '获取作品列表失败',
                    icon: 'none',
                });
            } finally {
                this.isLoading = false;
            }
        },

        // 加载更多作品
        loadMoreWorks() {
            if (!this.hasMoreWorks || this.isLoading) return;

            this.page++;
            this.getUserWorksList(true);
        },

        // 关注/取消关注用户
        async handleFollow() {
            try {
                const res = await this.$api.followUser({
                    follow_user_id: this.userId,
                    access_token: uni.getStorageSync('token'),
                });

                this.isFollowed = !this.isFollowed;

                uni.showToast({
                    title: this.isFollowed ? '关注成功' : '取消关注成功',
                    icon: 'success',
                });
            } catch (error) {
                console.error('关注操作失败:', error);
                uni.showToast({
                    title: '操作失败，请重试',
                    icon: 'none',
                });
            }
        },

        // 显示VIP弹窗
        showVipPopup() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }

            // 检查是否已经是会员
            if (this.isValidMember) {
                // 如果已经是会员，直接显示微信号
                this.showWechatInfo();
                return;
            }

            this.showVip = true;
        },

        // 显示微信号信息
        showWechatInfo() {
            this.showWechatInfoPopup = true;
        },

        // 显示礼物/聊天弹窗
        showGiftChatPopup() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            this.showGiftChat = true;
        },

        // 关闭弹窗
        handleClose() {
            this.showVip = false;
            this.showGiftChat = false;
        },

        // 关闭微信信息弹窗
        handleWechatInfoClose() {
            this.showWechatInfoPopup = false;
        },

        // 单次支付处理
        handleSinglePay() {
            console.log('单次支付');
        },

        // 会员支付处理
        handleMemberPay() {
            console.log('会员支付');
        },

        // 送礼物处理
        handleSendGift() {
            console.log('送礼物');
        },

        // 私聊功能
        async handlePrivateChat() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            if (!this.isValidMember) {
                this.showVip = true;
                return;
            }
            const res = await this.$api.initSession({
                access_token: uni.getStorageSync('token'),
                to_uid: this.info.uid,
            });
            const sessionId = res.data.msg_session_id;
            // 导航到聊天页面
            uni.navigateTo({
                url: `/views/moments/chat?sessionId=${sessionId}&userId=${this.info.uid}&username=${encodeURIComponent(this.info.nickname)}&avatar=${encodeURIComponent(this.info.headimgurl || '')}`,
            });
        },

        previewImage() {
            uni.previewImage({
                urls: [this.info.headimgurl],
                current: this.info.headimgurl,
            });
        },

        // 显示打赏弹窗
        showRewardPopup() {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }

            // 检查是否是自己的作品
            if (this.info && this.info.uid === this.userInfo.id) {
                uni.showToast({
                    title: '不能给自己的作品打赏',
                    icon: 'none',
                });
                return;
            }

            this.showReward = true;
        },

        // 打赏成功回调
        handleRewardSuccess(data) {
            console.log('打赏成功', data);

            uni.showToast({
                title: `成功赠送${data.gift.name}`,
                icon: 'none',
            });

            // 可以在这里更新作品的打赏统计
            // 或者刷新作品详情
            // this.getWorkDetail();
        },
        // 处理登录弹窗关闭
        handleLoginPopupClose() {
            this.showLoginPopup = false;
        },

        // 处理登录操作
        handleLoginAction() {
            // 登录弹窗已经会导航到登录页面，这里可以添加额外的逻辑
            console.log('用户点击了登录按钮');
        },

        // 格式化地址字符串，统一为"市-区"格式
        formatAddressString(address) {
            if (!address || !address.trim()) return '';

            // 如果地址已经是"市-区"格式，直接返回
            if (address.includes('-') && address.split('-').length === 2) {
                const parts = address.split('-');
                let cityPart = parts[0].trim();
                let areaPart = parts[1].trim();

                // 处理市名称
                if (cityPart.includes('市')) {
                    cityPart = cityPart.replace('市', '');
                }
                cityPart = cityPart.substring(0, 4);

                // 处理区名称
                areaPart = areaPart.substring(0, 2);

                return cityPart + '-' + areaPart;
            }

            // 如果是完整地址，尝试提取市区信息
            return this.parseFullAddress(address);
        },

        // 解析完整地址，提取市区信息
        parseFullAddress(address) {
            if (!address || !address.trim()) return '';

            // 正则表达式匹配省市区结构
            const patterns = [
                // 自治区格式：自治区+市+区（优先匹配，避免被省格式误匹配）
                /(.+?自治区)(.+?市)(.+?[区县])/,
                // 完整格式：省+市+区
                /(.+?省)(.+?市)(.+?[区县])/,
                // 直辖市格式：市+区
                /^(北京市|上海市|天津市|重庆市)(.+?[区县])/,
                // 简化格式：市+区（没有省）
                /^(.+?市)(.+?[区县])/
            ];

            let cityName = '';
            let areaName = '';
            let provinceName = '';

            for (let pattern of patterns) {
                const match = address.match(pattern);
                if (match) {
                    if (pattern.source.includes('自治区')) {
                        provinceName = match[1] || '';
                        cityName = match[2] || '';
                        areaName = match[3] || '';
                    } else if (pattern.source.includes('省')) {
                        provinceName = match[1] || '';
                        cityName = match[2] || '';
                        areaName = match[3] || '';
                    } else if (pattern.source.includes('北京市|上海市|天津市|重庆市')) {
                        cityName = match[1] || '';
                        areaName = match[2] || '';
                    } else {
                        cityName = match[1] || '';
                        areaName = match[2] || '';
                    }
                    break;
                }
            }

            // 如果没有匹配到，尝试简单的文本分析
            if (!cityName || !areaName) {
                const cityIndex = address.indexOf('市');
                const areaIndex = Math.max(address.indexOf('区'), address.indexOf('县'));

                if (cityIndex > 0 && areaIndex > cityIndex) {
                    let startIndex = 0;
                    const provinceIndex = Math.max(address.indexOf('省'), address.indexOf('自治区'));
                    if (provinceIndex >= 0) {
                        startIndex = provinceIndex + (address.indexOf('省') >= 0 ? 1 : 3);
                    }

                    cityName = address.substring(startIndex, cityIndex + 1);
                    areaName = address.substring(cityIndex + 1, areaIndex + 1);
                }
            }

            // 格式化处理
            if (cityName && areaName) {
                // 处理市名称
                if (cityName.includes('市')) {
                    cityName = cityName.replace('市', '');
                }
                cityName = cityName.substring(0, 4);

                // 处理区名称
                if (areaName.includes('区')) {
                    areaName = areaName.replace('区', '');
                } else if (areaName.includes('县')) {
                    areaName = areaName.replace('县', '');
                }
                areaName = areaName.substring(0, 2);

                // 特殊处理自治区
                if (provinceName && (provinceName.includes('新疆') || provinceName.includes('内蒙古') ||
                    provinceName.includes('西藏') || provinceName.includes('宁夏') || provinceName.includes('广西'))) {
                    let regionName = provinceName;
                    if (regionName.includes('维吾尔自治区')) {
                        regionName = '新疆';
                    } else if (regionName.includes('内蒙古自治区')) {
                        regionName = '内蒙古';
                    } else if (regionName.includes('西藏自治区')) {
                        regionName = '西藏';
                    } else if (regionName.includes('宁夏回族自治区')) {
                        regionName = '宁夏';
                    } else if (regionName.includes('广西壮族自治区')) {
                        regionName = '广西';
                    }
                    return regionName + '-' + areaName;
                }

                return cityName + '-' + areaName;
            }

            // 如果解析失败，返回前8个字符作为备用
            return address.substring(0, 8);
        },

        // 格式化用户信息中的地址
        formatUserLocation() {
            if (!this.info) return '未知地区';

            const user = this.info;
            console.log(user)
            let result = '';

            // 处理省/自治区信息
            let provinceName = '';
            if (user.province_code_txt && user.province_code_txt.name) {
                provinceName = user.province_code_txt.name;
            }

            // 处理市信息
            if (user.city_code_txt && user.city_code_txt.name) {
                let cityName = user.city_code_txt.name;
                let areaName = user.area_code_txt?.name || '';

                // 特殊处理自治区
                if (provinceName.includes('新疆') || provinceName.includes('内蒙古') || provinceName.includes('西藏') || provinceName.includes('宁夏') || provinceName.includes('广西')) {
                    // 自治区名称处理
                    let regionName = provinceName;
                    if (regionName.includes('维吾尔自治区')) {
                        regionName = '新疆';
                    } else if (regionName.includes('内蒙古自治区')) {
                        regionName = '内蒙古';
                    } else if (regionName.includes('西藏自治区')) {
                        regionName = '西藏';
                    } else if (regionName.includes('宁夏回族自治区')) {
                        regionName = '宁夏';
                    } else if (regionName.includes('广西壮族自治区')) {
                        regionName = '广西';
                    }

                    // 城市名称处理
                    if (cityName.includes('市')) {
                        cityName = cityName.replace('市', '');
                    }
                    if (cityName.includes('地区')) {
                        cityName = cityName.replace('地区', '');
                    }
                    if (cityName.includes('州')) {
                        cityName = cityName.replace('州', '');
                    }
                    // 区名称处理
                    if (areaName.includes('市')) {
                        areaName = areaName.replace('市', '');
                    }
                    if (areaName.includes('地区')) {
                        areaName = areaName.replace('地区', '');
                    }
                    if (areaName.includes('州')) {
                        areaName = areaName.replace('州', '');
                    }

                    result = regionName + '-' + areaName;
                } else {
                    // 普通省市处理
                    if (cityName.includes('市')) {
                        cityName = cityName.replace('市', '');
                    }
                    result = cityName.substring(0, 4);

                    // 处理区信息
                    if (user.area_code_txt && user.area_code_txt.name) {
                        let areaName = user.area_code_txt.name;
                        // 取前2位
                        areaName = areaName.substring(0, 2);
                        result += '-' + areaName;
                    }
                }
            } else {
                result = '未知地区';
            }

            return result;
        },
    },
};
</script>

<style lang="scss">
/* 定义全局变量 */
$primary-gradient: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
$primary-blue: #8966ef;
$primary-purple: #a584f2;
$primary-pink: #f0cc6c;
$gold-color: #f0cc6c;
$text-light: #ffffff;
$text-medium: rgba(255, 255, 255, 0.8);
$text-dark: #333333;
$bg-light: #f5f7fa;

.user-profile {
    background-color: $bg-light;
    min-height: 100vh;
    padding-bottom: 30rpx;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content {
    padding: 30rpx 24rpx;
}

/* 个人信息区域 */
.profile-info {
    display: flex;
    flex-direction: column;
    background: linear-gradient(to right, #ffffff, #f9fbff);
    border-radius: 20rpx;
    padding: 24rpx 20rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 10rpx 30rpx rgba(137, 102, 239, 0.1);
    // border-left: 6rpx solid $primary-blue;
    animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20rpx);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.user-info-section {
    display: flex;
    flex-direction: column;
}

.user-main-info {
    display: flex;
}

.avatar-container {
    margin-right: 20rpx;
    position: relative;

    .custom-avatar {
        border: 4rpx solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 6rpx 20rpx rgba(137, 102, 239, 0.2);
    }
}

.info-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* 昵称和在线时间行 */
.user-nickname-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 12rpx;
}

.online-time {
    font-size: 24rpx;
    color: #999;
    background: #f5f5f5;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
    margin-left: 12rpx;
}

.user-nickname {
    font-size: 34rpx;
    font-weight: 600;
    color: #333;
}

/* 第二行：性别、年龄、位置 */
.user-detail-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.gender-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    width: 36rpx;
    height: 36rpx;
    margin-right: 12rpx;

    &.male {
        background-color: #07c160;
    }

    &.female {
        background-color: #ff5597;
    }
}

.info-tag {
    display: flex;
    align-items: center;
    background-color: rgba(137, 102, 239, 0.1);
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    margin-right: 12rpx;
}

.info-tag text {
    font-size: 22rpx;
    color: #8966ef;
    margin-left: 4rpx;
}

.location-tag {
    max-width: 300rpx;
}

.location-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 250rpx;
}

.divider {
    height: 1px;
    background: linear-gradient(to right, rgba(137, 102, 239, 0.2), rgba(165, 132, 242, 0.2), rgba(137, 102, 239, 0.1));
    margin: 16rpx 0;
    width: 100%;
}

.user-intro {
    font-size: 30rpx;
    color: #666;
    margin-bottom: 16rpx;
    line-height: 1.5;
    padding: 0 4rpx;
}

.user-tags {
    display: flex;
    flex-wrap: wrap;
    // margin-bottom: 20rpx;
}

/* 操作按钮区域 */
.action-buttons-section {
    margin-top: 20rpx;
}

.action-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 20rpx;
    &.own-moment-actions {
        justify-content: flex-end;
        margin-top: 16rpx;
    }
    button {
        margin: 0;
        padding: 0;
    }
}

.tag-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    padding: 6rpx 16rpx;
    border-radius: 24rpx;
    margin-right: 12rpx;
    margin-bottom: 10rpx;
    box-shadow: 0 4rpx 10rpx rgba(137, 102, 239, 0.2);

    text {
        font-size: 22rpx;
        color: #ffffff;
        font-weight: 500;
    }
}

.private-chat-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
}

/* 渐变按钮样式 */
.gradient-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    // padding: 8rpx 16rpx;
    border-radius: 24rpx;
    border: none;
    color: $text-light;
    font-size: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    width: 140rpx;

    text {
        margin-left: 6rpx;
        font-weight: 500;
    }

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    }
}

/* 私聊按钮 */
.private-chat-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 微信按钮 */
.wechat-btn {
    background: linear-gradient(135deg, #07c160, #10d56a);
    box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.3);
}

/* 礼物按钮 */
.gift-btn {
    background: linear-gradient(135deg, #f0cc6c, #edc353);
    box-shadow: 0 4rpx 12rpx rgba(240, 204, 108, 0.3);
}

/* 打赏按钮 */
.reward-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 关注按钮 */
.follow-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 取消关注按钮 */
.unfollow-btn {
    background: linear-gradient(135deg, #999999, #777777);
    box-shadow: 0 4rpx 12rpx rgba(153, 153, 153, 0.3);
}

/* 点赞按钮 */
.like-btn {
    // background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    // box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

/* 取消点赞按钮 */
.unlike-btn {
    background: linear-gradient(135deg, #999999, #777777);
    box-shadow: 0 4rpx 12rpx rgba(153, 153, 153, 0.3);
}

/* 编辑按钮 */
.edit-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 状态按钮 */
.status-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
}

/* 删除按钮 */
.delete-btn {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
}

.btn-hover {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    opacity: 0.9;
}

/* 作品列表区域 */
.works-section {
    background: linear-gradient(to bottom, #ffffff, #f9fbff);
    border-radius: 20rpx;
    padding: 36rpx 30rpx;
    box-shadow: 0 10rpx 30rpx rgba(137, 102, 239, 0.08);
    border-top: 6rpx solid $primary-purple;
    position: relative;
    overflow: hidden;
    animation: slideIn 0.6s ease-out;
}

.section-header {
    display: flex;
    align-items: center;
    padding-bottom: 24rpx;
    margin-bottom: 24rpx;
    border-bottom: 1rpx solid rgba(137, 102, 239, 0.2);
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: $text-dark;
    margin-left: 12rpx;
    letter-spacing: 1rpx;
}

.user-grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10rpx;
}

.user-grid-item {
    width: 50%;
    padding: 0 10rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
}

.load-more {
    text-align: center;
    margin-top: 30rpx;
}

.load-more-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12rpx 36rpx;
    border-radius: 30rpx;
    background-color: #f5f7fa;
    color: #8966ef;
    font-size: 26rpx;
    box-shadow: 0 2rpx 8rpx rgba(137, 102, 239, 0.1);
    transition: all 0.3s;
}

.load-more-btn:active {
    background-color: #edf1f7;
    transform: translateY(2rpx);
}

.load-more-btn.loading {
    opacity: 0.8;
}

.empty-works {
    padding: 60rpx 0;
    text-align: center;
}
</style>
